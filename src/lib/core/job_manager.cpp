/**
 * @file job_manager.cpp
 * @brief Implementation of job management system for OMOP ETL pipeline
 * <AUTHOR> Cancer Data Engineering
 * @date 2025
 */

#include "job_manager.h"
#include "common/utilities.h"
#include "common/exceptions.h"
#include "common/logging.h"
#include <algorithm>
#include <filesystem>
#include <fstream>
#include <iomanip>
#include <thread>
#include <chrono>
#include <sstream>
#include <nlohmann/json.hpp>
#include <yaml-cpp/yaml.h>
#include "component_factory.h"

namespace omop::core {

// Job implementation
Job::Job(const JobConfig& config, std::unique_ptr<ETLPipeline> pipeline)
    : config_(config),
      pipeline_(std::move(pipeline)),
      creation_time_(std::chrono::system_clock::now()) {

    if (config_.job_id.empty()) {
        config_.job_id = omop::common::CryptoUtils::generate_uuid();
    }

    checkpoint_path_ = "/tmp/omop-etl/checkpoints/" + config_.job_id + ".checkpoint";
}

void Job::setStatus(JobStatus status) {
    updateStatus(status);
}

void Job::updateStatus(JobStatus new_status) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    JobStatus old_status = status_.exchange(new_status);

    if (new_status == JobStatus::Running && old_status != JobStatus::Running) {
        start_time_ = std::chrono::system_clock::now();
    } else if ((new_status == JobStatus::Completed || 
                new_status == JobStatus::Failed ||
                new_status == JobStatus::Cancelled) &&
               (old_status == JobStatus::Running || old_status == JobStatus::Paused)) {
        end_time_ = std::chrono::system_clock::now();
    }
}

JobStatistics Job::getStatistics() const {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    return statistics_;
}

void Job::updateStatistics(const JobStatistics& stats) {
    std::lock_guard<std::mutex> lock(stats_mutex_);
    statistics_ = stats;

    // Calculate elapsed time
    if (start_time_.time_since_epoch().count() > 0) {
        auto now = (end_time_.time_since_epoch().count() > 0) ? end_time_ : std::chrono::system_clock::now();
        statistics_.elapsed_time = now - start_time_;

        // Calculate processing rate
        if (statistics_.elapsed_time.count() > 0) {
            statistics_.processing_rate = static_cast<double>(statistics_.total_records_processed) /
                                        statistics_.elapsed_time.count();
        }
    }
}

bool Job::canRetry() const {
    return retry_count_ < config_.max_retries &&
           (status_ == JobStatus::Failed || status_ == JobStatus::Cancelled);
}

bool Job::saveCheckpoint() {
    if (!config_.enable_checkpointing) {
        return true;
    }

    try {
        // Create checkpoint directory if it doesn't exist
        std::filesystem::create_directories(std::filesystem::path(checkpoint_path_).parent_path());

        // Create checkpoint data
        nlohmann::json checkpoint;
        checkpoint["job_id"] = config_.job_id;
        checkpoint["status"] = static_cast<int>(status_.load());
        checkpoint["retry_count"] = retry_count_;
        checkpoint["statistics"] = {
            {"total_records_processed", statistics_.total_records_processed},
            {"successful_records", statistics_.successful_records},
            {"failed_records", statistics_.failed_records},
            {"skipped_records", statistics_.skipped_records}
        };

        // Add pipeline state if available
        if (pipeline_) {
            auto execution_stats = pipeline_->get_execution_stats();
            
            // Convert execution stats to job info for compatibility
            JobInfo job_info;
            job_info.job_id = execution_stats.pipeline_id;
            job_info.status = static_cast<JobStatus>(execution_stats.status);
            job_info.total_records = execution_stats.total_records_processed;
            job_info.processed_records = execution_stats.successful_records;
            job_info.error_records = execution_stats.failed_records;
            job_info.error_messages = execution_stats.errors;
            checkpoint["pipeline_state"] = {
                {"processed_records", job_info.processed_records},
                {"error_records", job_info.error_records},
                {"total_records", job_info.total_records}
            };
        }

        // Write checkpoint to file
        std::ofstream file(checkpoint_path_);
        if (!file.is_open()) {
            return false;
        }

        file << checkpoint.dump(4);
        file.close();

        return true;
    } catch (const std::exception& e) {
        // Log error but don't fail the job
        auto logger = omop::common::Logger::get("omop-job-manager");
        logger->error("Failed to save checkpoint for job {}: {}", config_.job_id, e.what());
        return false;
    }
}

bool Job::loadCheckpoint() {
    if (!config_.enable_checkpointing || !std::filesystem::exists(checkpoint_path_)) {
        return false;
    }

    try {
        std::ifstream file(checkpoint_path_);
        if (!file.is_open()) {
            return false;
        }

        nlohmann::json checkpoint;
        file >> checkpoint;
        file.close();

        // Verify job ID matches
        if (checkpoint["job_id"] != config_.job_id) {
            return false;
        }

        // Restore state
        status_ = static_cast<JobStatus>(checkpoint["status"].get<int>());
        retry_count_ = checkpoint["retry_count"];

        // Restore statistics
        statistics_.total_records_processed = checkpoint["statistics"]["total_records_processed"];
        statistics_.successful_records = checkpoint["statistics"]["successful_records"];
        statistics_.failed_records = checkpoint["statistics"]["failed_records"];
        statistics_.skipped_records = checkpoint["statistics"]["skipped_records"];

        return true;
    } catch (const std::exception& e) {
        auto logger = omop::common::Logger::get("omop-job-manager");
        logger->error("Failed to load checkpoint for job {}: {}", config_.job_id, e.what());
        return false;
    }
}

// JobManager implementation
JobManager::JobManager(std::shared_ptr<common::ConfigurationManager> config,
                       std::shared_ptr<common::Logger> logger)
    : config_(config),
      logger_(logger) {

    // Load configuration
    max_concurrent_jobs_ = config_->get_value_or<size_t>("job_manager.max_concurrent_jobs", 4);
}

JobManager::~JobManager() {
    stop();
}

bool JobManager::start() {
    if (running_.exchange(true)) {
        return false; // Already running
    }

    logger_->info("Starting job manager with {} worker threads", max_concurrent_jobs_);

    // Start worker threads
    for (size_t i = 0; i < max_concurrent_jobs_; ++i) {
        worker_threads_.emplace_back(&JobManager::workerThread, this);
    }

    return true;
}

void JobManager::stop() {
    if (!running_.exchange(false)) {
        return; // Already stopped
    }

    logger_->info("Stopping job manager");

    // Wake up all worker threads
    queue_cv_.notify_all();

    // Wait for worker threads to finish
    for (auto& thread : worker_threads_) {
        if (thread.joinable()) {
            thread.join();
        }
    }

    worker_threads_.clear();
}

std::string JobManager::submitJob(const JobConfig& config) {
    // Create pipeline from configuration
    auto pipeline = createPipeline(config.pipeline_config_path);
    if (!pipeline) {
        throw common::ConfigurationException("Failed to create pipeline from configuration", "pipeline_creation");
    }

    // Create job
    auto job = std::make_shared<Job>(config, std::move(pipeline));

    // Add to job registry
    {
        std::lock_guard<std::mutex> lock(jobs_mutex_);
        jobs_[job->getId()] = job;
    }

    // Add to queue
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        job->setStatus(JobStatus::Created);
        
        // Use a temporary priority queue to maintain order
        std::priority_queue<std::shared_ptr<Job>, std::vector<std::shared_ptr<Job>>, JobPriorityComparator> temp_queue(job_comparator_);
        
        // Move existing jobs to temp queue
        for (const auto& existing_job : job_queue_) {
            temp_queue.push(existing_job);
        }
        temp_queue.push(job);
        
        // Rebuild deque in priority order
        job_queue_.clear();
        while (!temp_queue.empty()) {
            job_queue_.push_back(temp_queue.top());
            temp_queue.pop();
        }
    }

    // Notify worker threads
    queue_cv_.notify_one();

    logger_->info("Submitted job {} with priority {}", job->getId(),
                 static_cast<int>(job->getConfig().priority));

    return job->getId();
}

std::shared_ptr<Job> JobManager::getJob(const std::string& job_id) const {
    std::lock_guard<std::mutex> lock(jobs_mutex_);
    auto it = jobs_.find(job_id);
    return (it != jobs_.end()) ? it->second : nullptr;
}

std::vector<std::shared_ptr<Job>> JobManager::getAllJobs() const {
    std::lock_guard<std::mutex> lock(jobs_mutex_);
    std::vector<std::shared_ptr<Job>> result;
    result.reserve(jobs_.size());

    for (const auto& [id, job] : jobs_) {
        result.push_back(job);
    }

    return result;
}

std::vector<std::shared_ptr<Job>> JobManager::getJobsByStatus(JobStatus status) const {
    std::lock_guard<std::mutex> lock(jobs_mutex_);
    std::vector<std::shared_ptr<Job>> result;

    for (const auto& [id, job] : jobs_) {
        if (job->getStatus() == status) {
            result.push_back(job);
        }
    }

    return result;
}

bool JobManager::cancelJob(const std::string& job_id) {
    auto job = getJob(job_id);
    if (!job) {
        return false;
    }

    JobStatus current_status = job->getStatus();
    if (current_status == JobStatus::Completed ||
        current_status == JobStatus::Failed ||
        current_status == JobStatus::Cancelled) {
        return false; // Job already finished
    }

    job->setStatus(JobStatus::Cancelled);

    // If job is running, signal the pipeline to stop
    if (current_status == JobStatus::Running && job->getPipeline()) {
        job->getPipeline()->stop();
    }

    logger_->info("Cancelled job {}", job_id);
    notifyJobStatusChange(job_id, current_status, JobStatus::Cancelled);

    return true;
}

bool JobManager::pauseJob(const std::string& job_id) {
    auto job = getJob(job_id);
    if (!job) {
        logger_->warn("Pause failed: Job {} not found", job_id);
        return false;
    }
    
    auto current_status = job->getStatus();
    logger_->debug("Attempting to pause job {} with status {}", job_id, static_cast<int>(current_status));
    
    if (current_status != JobStatus::Running) {
        logger_->warn("Pause failed: Job {} is not running (status: {})", job_id, static_cast<int>(current_status));
        return false;
    }

    job->setStatus(JobStatus::Paused);

    if (job->getPipeline()) {
        logger_->debug("Pausing pipeline for job {}", job_id);
        job->getPipeline()->pause();
    } else {
        logger_->warn("No pipeline found for job {} during pause", job_id);
    }

    logger_->info("Paused job {}", job_id);
    notifyJobStatusChange(job_id, JobStatus::Running, JobStatus::Paused);

    return true;
}

bool JobManager::resumeJob(const std::string& job_id) {
    auto job = getJob(job_id);
    if (!job) {
        logger_->warn("Resume failed: Job {} not found", job_id);
        return false;
    }
    
    auto current_status = job->getStatus();
    logger_->debug("Attempting to resume job {} with status {}", job_id, static_cast<int>(current_status));
    
    if (current_status != JobStatus::Paused) {
        logger_->warn("Resume failed: Job {} is not paused (status: {})", job_id, static_cast<int>(current_status));
        return false;
    }

    // Resume the existing pipeline instead of re-queueing
    if (job->getPipeline()) {
        logger_->debug("Resuming pipeline for job {}", job_id);
        job->getPipeline()->resume();
        job->setStatus(JobStatus::Running);
        
        logger_->info("Resumed job {}", job_id);
        notifyJobStatusChange(job_id, JobStatus::Paused, JobStatus::Running);
        return true;
    }

    logger_->warn("Resume failed: Job {} has no pipeline", job_id);
    return false;
}

bool JobManager::retryJob(const std::string& job_id) {
    auto job = getJob(job_id);
    if (!job || !job->canRetry()) {
        logger_->debug("RetryJob: cannot retry job {} (canRetry: {})", job_id, job ? job->canRetry() : false);
        return false;
    }

    job->incrementRetryCount();
    logger_->info("Retrying job {} (retry count now {})", job_id, job->getRetryCount());
    job->setStatus(JobStatus::Created); // Reset to Created for re-queueing

    // Create new pipeline instance for retry
    auto pipeline = createPipeline(job->getConfig().pipeline_config_path);
    if (!pipeline) {
        return false;
    }
    job->pipeline_ = std::move(pipeline);
    
    // Load checkpoint if available to restore state
    // Note: Don't load checkpoint during retry as it would reset the retry count
    // The checkpoint is loaded when the job actually starts execution

    // Re-queue the job
    {
        std::lock_guard<std::mutex> lock(queue_mutex_);
        // Insert in sorted order
        auto insert_pos = std::lower_bound(job_queue_.begin(), job_queue_.end(),
                                         job, job_comparator_);
        job_queue_.insert(insert_pos, job);
    }

    queue_cv_.notify_one();

    logger_->info("Retrying job {} (attempt {})", job_id, job->getRetryCount() + 1);
    notifyJobStatusChange(job_id, JobStatus::Failed, JobStatus::Created);

    return true;
}

size_t JobManager::getActiveJobCount() const {
    return active_jobs_.load();
}

size_t JobManager::getQueuedJobCount() const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(queue_mutex_));
    return job_queue_.size();
}

void JobManager::setMaxConcurrentJobs(size_t max_jobs) {
    max_concurrent_jobs_ = max_jobs;
    // Note: This doesn't affect already running threads
}

void JobManager::registerJobEventCallback(
    std::function<void(const std::string&, JobStatus, JobStatus)> callback) {
    std::lock_guard<std::mutex> lock(callbacks_mutex_);
    event_callbacks_.push_back(callback);
}

size_t JobManager::cleanupOldJobs(std::chrono::hours age) {
    std::lock_guard<std::mutex> lock(jobs_mutex_);
    auto now = std::chrono::system_clock::now();
    size_t cleaned = 0;

    for (auto it = jobs_.begin(); it != jobs_.end(); ) {
        auto job = it->second;
        if ((job->getStatus() == JobStatus::Completed ||
             job->getStatus() == JobStatus::Failed ||
             job->getStatus() == JobStatus::Cancelled) &&
            (now - job->getEndTime()) > age) {
            it = jobs_.erase(it);
            ++cleaned;
        } else {
            ++it;
        }
    }

    logger_->info("Cleaned up {} old jobs", cleaned);
    return cleaned;
}

void JobManager::workerThread() {
    logger_->debug("Worker thread started");

    while (running_) {
        std::shared_ptr<Job> job;

        // Get next job from queue
        {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            queue_cv_.wait(lock, [this] { return !job_queue_.empty() || !running_; });

            if (!running_) {
                break;
            }

            if (!job_queue_.empty()) {
                job = job_queue_.front();
                job_queue_.pop_front();
            }
        }

        if (job) {
            // Execute the job
            active_jobs_++;
            JobExecutionContext context{job, logger_, nullptr, false};
            executeJob(context);
            active_jobs_--;
        }
        
        // Yield to prevent CPU spinning
        if (!job) {
            std::this_thread::yield();
        }
    }

    logger_->debug("Worker thread stopped");
}

void JobManager::executeJob(JobExecutionContext& context) {
    auto job = context.job;
    auto old_status = job->getStatus();

    try {
        logger_->info("Starting execution of job {}", job->getId());
        job->setStatus(JobStatus::Initializing);
        notifyJobStatusChange(job->getId(), old_status, JobStatus::Initializing);

        // Load checkpoint if available (but not during retry to preserve retry count)
        if (job->getConfig().enable_checkpointing && job->getRetryCount() == 0) {
            job->loadCheckpoint();
        }

        // Initialize pipeline
        auto pipeline = job->getPipeline();
        if (!pipeline) {
            throw common::ConfigurationException("Pipeline is null", "pipeline_execution");
        }

        // Run the pipeline
        job->setStatus(JobStatus::Running);
        notifyJobStatusChange(job->getId(), JobStatus::Initializing, JobStatus::Running);

        // Use the new pipeline interface
        pipeline->start(); // Start the pipeline
        auto status = pipeline->wait_for_completion(); // Wait for completion
        auto execution_stats = pipeline->get_execution_stats(); // Get execution stats
        
        // Convert execution stats to job info for compatibility
        JobInfo job_info;
        job_info.job_id = execution_stats.pipeline_id;
        job_info.status = static_cast<JobStatus>(execution_stats.status);
        job_info.total_records = execution_stats.total_records_processed;
        job_info.processed_records = execution_stats.successful_records;
        job_info.error_records = execution_stats.failed_records;
        job_info.error_messages = execution_stats.errors;

        // Update job statistics
        JobStatistics stats;
        stats.total_records_processed = job_info.total_records;
        stats.successful_records = job_info.processed_records;
        stats.failed_records = job_info.error_records;
        job->updateStatistics(stats);

        // Check the final status from the pipeline
        if (job_info.status == JobStatus::Completed) {
            handleJobCompletion(job);
        } else if (job_info.status == JobStatus::Cancelled) {
            // Job was cancelled during execution
            job->setStatus(JobStatus::Cancelled);
            logger_->info("Job {} was cancelled during execution", job->getId());
            notifyJobStatusChange(job->getId(), JobStatus::Running, JobStatus::Cancelled);
        } else {
            // Job failed
            if (!job_info.error_messages.empty()) {
                job->setErrorMessage(job_info.error_messages[0]);
            }
            handleJobFailure(job);
        }

    } catch (const std::exception& e) {
        logger_->error("Job {} failed with exception: {}", job->getId(), e.what());
        job->setErrorMessage(e.what());
        handleJobFailure(job);
    }
}

std::unique_ptr<ETLPipeline> JobManager::createPipeline(const std::string& config_path) {
    try {
        if (config_path.empty()) {
            // Create default pipeline
            return std::make_unique<ETLPipeline>();
        }

        // Load configuration from file and create pipeline with config
        if (!std::filesystem::exists(config_path)) {
            logger_->error("Pipeline configuration file not found: {}", config_path);
            return nullptr;
        }

        try {
            // Parse YAML configuration file
            YAML::Node yaml_config = YAML::LoadFile(config_path);
            
            // Create pipeline builder
            PipelineBuilder builder;
            
            // Check for new pipeline.stages format
            if (yaml_config["pipeline"] && yaml_config["pipeline"]["stages"]) {
                auto stages = yaml_config["pipeline"]["stages"];
                if (!stages.IsSequence()) {
                    logger_->error("Pipeline stages must be a sequence");
                    return nullptr;
                }
                
                for (const auto& stage : stages) {
                    if (!stage["type"] || !stage["config"]) {
                        logger_->error("Each stage must have 'type' and 'config' fields");
                        return nullptr;
                    }
                    
                    std::string stage_type = stage["type"].as<std::string>();
                    auto stage_config = stage["config"];
                    
                    // Convert YAML config to std::unordered_map<std::string, std::any>
                    std::unordered_map<std::string, std::any> params;
                    for (auto it = stage_config.begin(); it != stage_config.end(); ++it) {
                        std::string key = it->first.as<std::string>();
                        if (it->second.IsScalar()) {
                            // Handle different scalar types
                            if (it->second.IsNull()) {
                                params[key] = std::any();
                            } else if (it->second.Tag() == "!") {
                                // String
                                params[key] = it->second.as<std::string>();
                            } else {
                                // Try to determine type
                                try {
                                    params[key] = it->second.as<int>();
                                } catch (...) {
                                    try {
                                        params[key] = it->second.as<double>();
                                    } catch (...) {
                                        try {
                                            params[key] = it->second.as<bool>();
                                        } catch (...) {
                                            params[key] = it->second.as<std::string>();
                                        }
                                    }
                                }
                            }
                        }
                    }
                    
                    if (stage_type == "extractor") {
                        if (!stage_config["type"]) {
                            logger_->error("Extractor stage must have config.type");
                            return nullptr;
                        }
                        std::string extractor_type = stage_config["type"].as<std::string>();
                        builder.with_extractor(extractor_type, params);
                    } else if (stage_type == "transformer") {
                        if (!stage_config["type"]) {
                            logger_->error("Transformer stage must have config.type");
                            return nullptr;
                        }
                        std::string transformer_type = stage_config["type"].as<std::string>();
                        builder.with_transformer(transformer_type, params);
                    } else if (stage_type == "loader") {
                        if (!stage_config["type"]) {
                            logger_->error("Loader stage must have config.type");
                            return nullptr;
                        }
                        std::string loader_type = stage_config["type"].as<std::string>();
                        builder.with_loader(loader_type, params);
                    } else {
                        logger_->error("Unknown stage type: {}", stage_type);
                        return nullptr;
                    }
                }
            } else {
                logger_->error("Pipeline configuration must use 'pipeline.stages' format");
                return nullptr;
            }
            
            return builder.build();
            
        } catch (const YAML::Exception& e) {
            logger_->error("Failed to parse YAML configuration: {}", e.what());
            return nullptr;
        }
        
    } catch (const std::exception& e) {
        logger_->error("Failed to create pipeline: {}", e.what());
        return nullptr;
    }
}

void JobManager::handleJobCompletion(std::shared_ptr<Job> job) {
    job->setStatus(JobStatus::Completed);

    // Save final checkpoint
    job->saveCheckpoint();

    logger_->info("Job {} completed successfully. Stats: {} records processed, {} succeeded, {} failed",
                 job->getId(),
                 job->getStatistics().total_records_processed,
                 job->getStatistics().successful_records,
                 job->getStatistics().failed_records);

    notifyJobStatusChange(job->getId(), JobStatus::Running, JobStatus::Completed);
}

void JobManager::handleJobFailure(std::shared_ptr<Job> job) {
    job->setStatus(JobStatus::Failed);

    // Save checkpoint for potential retry
    job->saveCheckpoint();

    logger_->error("Job {} failed: {}", job->getId(), job->getErrorMessage());

    notifyJobStatusChange(job->getId(), JobStatus::Running, JobStatus::Failed);

    // Auto-retry if configured
    if (job->canRetry() && job->getConfig().max_retries > 0) {
        logger_->info("Scheduling automatic retry for job {} after {} seconds",
                     job->getId(), job->getConfig().retry_delay.count());

        // Schedule retry asynchronously to avoid blocking the worker thread
        std::thread([this, job_id = job->getId(), delay = job->getConfig().retry_delay]() {
            std::this_thread::sleep_for(delay);
            retryJob(job_id);
        }).detach();
    }
}

void JobManager::notifyJobStatusChange(const std::string& job_id,
                                      JobStatus old_status,
                                      JobStatus new_status) {
    std::lock_guard<std::mutex> lock(callbacks_mutex_);
    for (const auto& callback : event_callbacks_) {
        try {
            callback(job_id, old_status, new_status);
        } catch (const std::exception& e) {
            logger_->error("Error in job event callback: {}", e.what());
        }
    }
}

void JobManager::addJob(std::shared_ptr<Job> job) {
    std::lock_guard<std::mutex> lock(jobs_mutex_);

    if (jobs_.find(job->getId()) != jobs_.end()) {
        throw common::ConfigurationException("Job with ID already exists", job->getId());
    }

    jobs_[job->getId()] = job;
    logger_->info("Added job: {}", job->getId());
}

void JobManager::removeJob(const std::string& job_id) {
    std::lock_guard<std::mutex> lock(jobs_mutex_);

    auto it = jobs_.find(job_id);
    if (it == jobs_.end()) {
        throw common::ConfigurationException("Job not found", job_id);
    }

    jobs_.erase(it);
    logger_->info("Removed job: {}", job_id);
}

void JobManager::executePipeline(std::unique_ptr<ETLPipeline> pipeline) {
    if (!pipeline) {
        throw common::ConfigurationException("Pipeline is null", "pipeline_execution");
    }

    try {
        pipeline->start(); // Start the pipeline
        pipeline->wait_for_completion(); // Wait for completion
    } catch (const std::exception& e) {
        logger_->error("Pipeline execution failed: {}", e.what());
        throw;
    }
}


} // namespace omop::core